"use client";

import { apiService } from "@/api";
import ItemPicker from "@/components/ItemPicker";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import { Separator } from "@/components/ui/separator";
import {
  PaginatedResponse,
  User,
  Warehouse,
  Product,
  Address,
} from "@/frontend-types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPicker } from "@/components/warehouses/MapPicker";

// Schema for creating a new user
const createUserSchema = z.object({
  name: z.string().min(1, "Name is required"),
  phone: z.string().min(1, "Phone is required"),
  countryId: z.number(),
});

// Schema for address - simplified for form
const addressSchema = z.object({
  apartment: z.string().nullable().optional(),
  block: z.string().nullable().optional(),
  streetName: z.string().min(1, "Street name is required"),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  lat: z.string(),
  long: z.string(),
});

type CreateUserForm = z.infer<typeof createUserSchema>;
type AddressForm = z.infer<typeof addressSchema>;

interface CartItem {
  product: Product;
  quantity: number;
  variationId?: number;
}

export default function CreateOrderPage() {
  // User state
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isCreateUserOpen, setIsCreateUserOpen] = useState(false);

  // Address state
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);
  const [isAddressDialogOpen, setIsAddressDialogOpen] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<{
    lat: number;
    lng: number;
  } | null>(null);

  // Warehouse state
  const [selectedWarehouse, setSelectedWarehouse] = useState<Warehouse | null>(
    null
  );

  // Cart state
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [couponCode, setCouponCode] = useState("");
  const [cartTotal, setCartTotal] = useState(0);
  const [discount, setDiscount] = useState(0);

  // Calculate cart total whenever items change
  useEffect(() => {
    const total = cartItems.reduce((sum, item) => {
      return sum + item.product.price * item.quantity;
    }, 0);
    setCartTotal(total);
  }, [cartItems]);

  // Forms
  const userForm = useForm<CreateUserForm>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      name: "",
      phone: "",
      countryId: 1,
    },
  });

  const addressForm = useForm<AddressForm>({
    resolver: zodResolver(addressSchema),
    defaultValues: {
      apartment: "",
      block: "",
      streetName: "",
      city: "",
      state: "",
      lat: "0",
      long: "0",
    },
  });

  // Internal state for hidden fields
  const [_zipCode] = useState("00000");
  const [_countryId] = useState(1);

  // Function to generate a random password
  const generateRandomPassword = () => {
    const length = 12;
    const charset =
      "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let password = "";
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  };

  // Function to create a new user
  const onCreateUser = async (data: CreateUserForm) => {
    try {
      const response = await apiService.post("auth/register", {
        ...data,
        password: generateRandomPassword(),
      });

      const user = response.data as User;
      setSelectedUser(user);
      setIsCreateUserOpen(false);
      toast.success("User created successfully");
      userForm.reset();
    } catch (error) {
      toast.error("Failed to create user", {
        description:
          error instanceof Error ? error.message : "Unknown error occurred",
      });
    }
  };

  // Function to fetch users for ItemPicker
  const fetchUsers = async (
    page: number,
    pageSize: number,
    searchQuery: string
  ): Promise<PaginatedResponse<User>> => {
    const response = await apiService.get("admin/users", {
      page,
      pageSize,
      search: searchQuery,
    });
    return response.data;
  };

  // Function to fetch warehouses for ItemPicker
  const fetchWarehouses = async (
    page: number,
    pageSize: number,
    searchQuery: string
  ): Promise<PaginatedResponse<Warehouse>> => {
    const response = await apiService.get("admin/warehouses", {
      params: {
        page,
        pageSize,
        search: searchQuery,
      },
    });
    return response.data;
  };

  // Function to fetch products for ItemPicker
  const fetchProducts = async (
    page: number,
    pageSize: number,
    searchQuery: string
  ): Promise<PaginatedResponse<Product>> => {
    const response = await apiService.get("products", {
      params: {
        page,
        pageSize,
        search: searchQuery,
        warehouseId: selectedWarehouse?.id,
      },
    });
    return response.data;
  };

  // Function to add product to cart
  const addToCart = (
    product: Product,
    quantity: number = 1,
    variationId?: number
  ) => {
    setCartItems((prev) => {
      const existingItem = prev.find(
        (item) =>
          item.product.id === product.id && item.variationId === variationId
      );

      if (existingItem) {
        return prev.map((item) =>
          item === existingItem
            ? { ...item, quantity: item.quantity + quantity }
            : item
        );
      }

      return [...prev, { product, quantity, variationId }];
    });
  };

  // Function to remove item from cart
  const removeFromCart = (productId: number, variationId?: number) => {
    setCartItems((prev) =>
      prev.filter(
        (item) =>
          !(item.product.id === productId && item.variationId === variationId)
      )
    );
  };

  // Function to update cart item quantity
  const updateCartItemQuantity = (
    productId: number,
    quantity: number,
    variationId?: number
  ) => {
    setCartItems((prev) =>
      prev.map((item) =>
        item.product.id === productId && item.variationId === variationId
          ? { ...item, quantity }
          : item
      )
    );
  };

  // Function to apply coupon
  const applyCoupon = async () => {
    try {
      const response = await apiService.post("coupons/validate", {
        code: couponCode,
        amount: cartTotal,
      });

      setDiscount(response.data.discount);
      toast.success("Coupon applied successfully");
    } catch (error) {
      toast.error("Failed to apply coupon", {
        description:
          error instanceof Error ? error.message : "Unknown error occurred",
      });
    }
  };

  // Function to place order
  const placeOrder = async () => {
    if (
      !selectedUser ||
      !selectedAddress ||
      !selectedWarehouse ||
      cartItems.length === 0
    ) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      const orderData = {
        userId: selectedUser.id,
        addressId: selectedAddress.id,
        warehouseId: selectedWarehouse.id,
        items: cartItems.map((item) => ({
          productId: item.product.id,
          variationId: item.variationId,
          quantity: item.quantity,
        })),
        couponCode: couponCode || undefined,
      };

      await apiService.post("orders", orderData);
      toast.success("Order placed successfully");

      // Reset form
      setSelectedUser(null);
      setSelectedAddress(null);
      setSelectedWarehouse(null);
      setCartItems([]);
      setCouponCode("");
      setDiscount(0);
    } catch (error) {
      toast.error("Failed to place order", {
        description:
          error instanceof Error ? error.message : "Unknown error occurred",
      });
    }
  };

  // Update address form with location data
  const handleLocationSelect = async (lat: string, lng: string) => {
    // Update selected location
    setSelectedLocation({
      lat: parseFloat(lat),
      lng: parseFloat(lng),
    });

    // Update form values
    addressForm.setValue("lat", lat);
    addressForm.setValue("long", lng);

    // Use Google Places API to get address details
    try {
      if (typeof window !== "undefined" && window.google) {
        const geocoder = new window.google.maps.Geocoder();
        const response = await geocoder.geocode({
          location: { lat: parseFloat(lat), lng: parseFloat(lng) },
        });

        if (response.results && response.results.length > 0) {
          const addressComponents = response.results[0].address_components;

          // Extract address components
          const getComponent = (type: string, shortName = false) => {
            const component = addressComponents.find((c) =>
              c.types.includes(type)
            );
            return component
              ? shortName
                ? component.short_name
                : component.long_name
              : "";
          };

          // Set address fields from Google Places API
          const streetNumber = getComponent("street_number");
          const route = getComponent("route");
          const streetName = `${streetNumber} ${route}`.trim();

          if (streetName) {
            addressForm.setValue("streetName", streetName);
          }

          const city =
            getComponent("locality") ||
            getComponent("administrative_area_level_2");
          if (city) {
            addressForm.setValue("city", city);
          }

          const state = getComponent("administrative_area_level_1");
          if (state) {
            addressForm.setValue("state", state);
          }
        }
      }
    } catch (error) {
      console.error("Error getting address details:", error);
      // If geocoding fails, just continue with the coordinates
    }
  };

  // Reset address form and location when dialog opens
  const handleAddressDialogOpen = () => {
    setSelectedLocation(null);
    addressForm.reset({
      apartment: "",
      block: "",
      streetName: "",
      city: "",
      state: "",
      lat: "0",
      long: "0",
    });
    setIsAddressDialogOpen(true);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Create Manual Order</h1>
      </div>

      <Separator />

      <div className="grid grid-cols-3 gap-6">
        {/* Left Column - Main Form */}
        <div className="col-span-2 space-y-6">
          {/* User Selection Section */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <ItemPicker<User>
                    title="Select Customer"
                    selectionMode="single"
                    columns={[
                      {
                        header: "Name",
                        accessorKey: "name",
                      },
                      {
                        header: "Phone",
                        accessorKey: "phone",
                      },
                      {
                        header: "Email",
                        accessorKey: "email",
                      },
                    ]}
                    fetchItems={fetchUsers}
                    onConfirmSelection={(selected) => {
                      setSelectedUser(selected as User);
                    }}
                    initialSelectedItems={selectedUser ? [selectedUser] : []}
                    keyField="id"
                    searchPlaceholder="Search by name or phone..."
                    renderTrigger={() => (
                      <Button
                        variant="outline"
                        className="w-full justify-between"
                      >
                        {selectedUser ? selectedUser.name : "Select Customer"}
                      </Button>
                    )}
                  />
                </div>
                <Button
                  variant="outline"
                  className="ml-4"
                  onClick={() => setIsCreateUserOpen(true)}
                >
                  Create New
                </Button>
              </div>

              {selectedUser && (
                <div className="mt-4">
                  <div className="text-sm text-muted-foreground">
                    Selected Customer:
                  </div>
                  <div className="mt-1">
                    <Badge variant="secondary">{selectedUser.name}</Badge>
                    <Badge variant="secondary" className="ml-2">
                      {selectedUser.phone}
                    </Badge>
                    {selectedUser.email && (
                      <Badge variant="secondary" className="ml-2">
                        {selectedUser.email}
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Address Selection Section */}
          {selectedUser && (
            <Card>
              <CardHeader>
                <CardTitle>Delivery Address</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <ItemPicker<Address>
                      title="Select Address"
                      selectionMode="single"
                      columns={[
                        {
                          header: "Address",
                          render: (address) => (
                            <div>
                              {address.streetName}, {address.city},{" "}
                              {address.state}
                            </div>
                          ),
                        },
                      ]}
                      fetchItems={async (page, pageSize, search) => {
                        const response = await apiService.get(`addresses`, {
                          overrideUserId: selectedUser.id,
                          page,
                          pageSize,
                          search,
                        });
                        return {
                          data: response.data,
                          total: response.data.length,
                          page,
                          pageSize,
                          totalPages: 1,
                        } satisfies PaginatedResponse<Address>;
                      }}
                      onConfirmSelection={(selected) => {
                        setSelectedAddress(selected as Address);
                      }}
                      initialSelectedItems={
                        selectedAddress ? [selectedAddress] : []
                      }
                      keyField="id"
                      searchPlaceholder="Search address..."
                      renderTrigger={() => (
                        <Button
                          variant="outline"
                          className="w-full justify-between"
                        >
                          {selectedAddress
                            ? `${selectedAddress.streetName}, ${selectedAddress.city}`
                            : "Select Address"}
                        </Button>
                      )}
                    />
                  </div>
                  <Button
                    variant="outline"
                    className="ml-4"
                    onClick={handleAddressDialogOpen}
                  >
                    Add New
                  </Button>
                </div>

                {/* Map Component */}
                {selectedAddress && (
                  <div className="mt-4 h-[300px] rounded-lg overflow-hidden border">
                    <MapPicker
                      initialLat={selectedAddress.lat}
                      initialLng={selectedAddress.long}
                      onLocationSelect={() => {}} // Read-only map
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Warehouse Selection */}
          {selectedAddress && (
            <Card>
              <CardHeader>
                <CardTitle>Select Warehouse</CardTitle>
              </CardHeader>
              <CardContent>
                <ItemPicker<Warehouse>
                  title="Select Warehouse"
                  selectionMode="single"
                  columns={[
                    {
                      header: "Name",
                      accessorKey: "name",
                    },
                    {
                      header: "Location",
                      render: (warehouse) => <div>{warehouse.address}</div>,
                    },
                  ]}
                  fetchItems={fetchWarehouses}
                  onConfirmSelection={(selected) => {
                    setSelectedWarehouse(selected as Warehouse);
                  }}
                  initialSelectedItems={
                    selectedWarehouse ? [selectedWarehouse] : []
                  }
                  keyField="id"
                  searchPlaceholder="Search warehouse..."
                  renderTrigger={() => (
                    <Button
                      variant="outline"
                      className="w-full justify-between"
                    >
                      {selectedWarehouse
                        ? selectedWarehouse.name
                        : "Select Warehouse"}
                    </Button>
                  )}
                />
              </CardContent>
            </Card>
          )}

          {/* Product Selection */}
          {selectedWarehouse && (
            <Card>
              <CardHeader>
                <CardTitle>Select Products</CardTitle>
              </CardHeader>
              <CardContent>
                <ItemPicker<Product>
                  title="Add Products"
                  selectionMode="multiple"
                  columns={[
                    {
                      header: "Name",
                      accessorKey: "name",
                    },
                    {
                      header: "Price",
                      render: (product) => <div>${product.price}</div>,
                    },
                    {
                      header: "Stock",
                      render: (product) => <div>{product.stock}</div>,
                    },
                  ]}
                  fetchItems={fetchProducts}
                  onConfirmSelection={(selected) => {
                    const products = Array.isArray(selected)
                      ? selected
                      : [selected];
                    products.forEach((product) => {
                      if (
                        !cartItems.some(
                          (item) => item.product.id === product.id
                        )
                      ) {
                        addToCart(product, 1);
                      }
                    });
                  }}
                  keyField="id"
                  searchPlaceholder="Search products..."
                />
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column - Cart */}
        <div className="col-span-1">
          <Card className="sticky top-6">
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {cartItems.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  No items in cart
                </div>
              ) : (
                <>
                  {/* Cart Items */}
                  <div className="space-y-4">
                    {cartItems.map((item) => (
                      <div
                        key={`${item.product.id}-${item.variationId}`}
                        className="flex items-start justify-between gap-4"
                      >
                        <div className="flex-1">
                          <div className="font-medium">{item.product.name}</div>
                          {item.variationId && (
                            <div className="text-sm text-muted-foreground">
                              Variation: {item.variationId}
                            </div>
                          )}
                          <div className="text-sm">
                            ${item.product.price} × {item.quantity}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              updateCartItemQuantity(
                                item.product.id,
                                Math.max(1, item.quantity - 1),
                                item.variationId
                              )
                            }
                          >
                            -
                          </Button>
                          <span className="w-8 text-center">
                            {item.quantity}
                          </span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              updateCartItemQuantity(
                                item.product.id,
                                item.quantity + 1,
                                item.variationId
                              )
                            }
                          >
                            +
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              removeFromCart(item.product.id, item.variationId)
                            }
                          >
                            ×
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Coupon Input */}
                  <div className="flex gap-2">
                    <Input
                      placeholder="Enter coupon code"
                      value={couponCode}
                      onChange={(e) => setCouponCode(e.target.value)}
                    />
                    <Button variant="outline" onClick={applyCoupon}>
                      Apply
                    </Button>
                  </div>

                  {/* Order Summary */}
                  <div className="space-y-2 pt-4 border-t">
                    <div className="flex justify-between">
                      <span>Subtotal</span>
                      <span>${cartTotal}</span>
                    </div>
                    {discount > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>Discount</span>
                        <span>-${discount}</span>
                      </div>
                    )}
                    <div className="flex justify-between font-medium">
                      <span>Total</span>
                      <span>${cartTotal - discount}</span>
                    </div>
                  </div>

                  {/* Place Order Button */}
                  <Button
                    className="w-full"
                    size="lg"
                    onClick={placeOrder}
                    disabled={
                      !selectedUser || !selectedAddress || !selectedWarehouse
                    }
                  >
                    Place Order
                  </Button>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Create User Dialog */}
      <Dialog open={isCreateUserOpen} onOpenChange={setIsCreateUserOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Customer</DialogTitle>
          </DialogHeader>
          <form
            onSubmit={userForm.handleSubmit(onCreateUser)}
            className="space-y-4"
          >
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                {...userForm.register("name")}
                placeholder="Enter customer name"
              />
              {userForm.formState.errors.name && (
                <p className="text-sm text-red-500">
                  {userForm.formState.errors.name.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                {...userForm.register("phone")}
                placeholder="Enter phone number"
              />
              {userForm.formState.errors.phone && (
                <p className="text-sm text-red-500">
                  {userForm.formState.errors.phone.message}
                </p>
              )}
            </div>

            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsCreateUserOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit">Create Customer</Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Create Address Dialog */}
      <Dialog open={isAddressDialogOpen} onOpenChange={setIsAddressDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Address</DialogTitle>
          </DialogHeader>
          <form
            onSubmit={addressForm.handleSubmit(async (data) => {
              try {
                const response = await apiService.post(`addresses`, {
                  ...data,
                  type: "SHIPPING", // Always use SHIPPING type
                  countryId: _countryId, // Use internal state
                  zipCode: _zipCode || "00000", // Use internal state or default
                  overrideUserId: selectedUser?.id,
                });
                setSelectedAddress(response.data);
                setIsAddressDialogOpen(false);
                setSelectedLocation(null);
                toast.success("Address added successfully");
                addressForm.reset();
              } catch (error) {
                toast.error("Failed to add address", {
                  description:
                    error instanceof Error
                      ? error.message
                      : "Unknown error occurred",
                });
              }
            })}
            className="space-y-4"
          >
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="block">Block</Label>
                <Input
                  id="block"
                  {...addressForm.register("block")}
                  placeholder="Enter block (optional)"
                />
                {addressForm.formState.errors.block && (
                  <p className="text-sm text-red-500">
                    {addressForm.formState.errors.block.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="apartment">Apartment</Label>
                <Input
                  id="apartment"
                  {...addressForm.register("apartment")}
                  placeholder="Enter apartment (optional)"
                />
                {addressForm.formState.errors.apartment && (
                  <p className="text-sm text-red-500">
                    {addressForm.formState.errors.apartment.message}
                  </p>
                )}
              </div>

              <div className="space-y-2 col-span-2">
                <Label htmlFor="streetName">Street Name *</Label>
                <Input
                  id="streetName"
                  {...addressForm.register("streetName")}
                  placeholder="Enter street name"
                />
                {addressForm.formState.errors.streetName && (
                  <p className="text-sm text-red-500">
                    {addressForm.formState.errors.streetName.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="city">City *</Label>
                <Input
                  id="city"
                  {...addressForm.register("city")}
                  placeholder="Enter city"
                />
                {addressForm.formState.errors.city && (
                  <p className="text-sm text-red-500">
                    {addressForm.formState.errors.city.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="state">State *</Label>
                <Input
                  id="state"
                  {...addressForm.register("state")}
                  placeholder="Enter state"
                />
                {addressForm.formState.errors.state && (
                  <p className="text-sm text-red-500">
                    {addressForm.formState.errors.state.message}
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <Label>Select Location</Label>
              <div className="h-[400px] rounded-lg border overflow-hidden">
                <MapPicker
                  initialLat={selectedLocation?.lat.toString()}
                  initialLng={selectedLocation?.lng.toString()}
                  onLocationSelect={handleLocationSelect}
                />
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsAddressDialogOpen(false);
                  setSelectedLocation(null);
                  addressForm.reset();
                }}
              >
                Cancel
              </Button>
              <Button type="submit">Create Address</Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
