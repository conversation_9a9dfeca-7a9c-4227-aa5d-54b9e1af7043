"use client";

import { apiService } from "@/api";
import ItemPicker from "@/components/ItemPicker";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import { Separator } from "@/components/ui/separator";
import {
  PaginatedResponse,
  User,
  Warehouse,
  Product,
  Address,
} from "@/frontend-types";

import { zodResolver } from "@hookform/resolvers/zod";
import { useState, useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPicker } from "@/components/warehouses/MapPicker";
import { Trash } from "lucide-react";

// Schema for creating a new user
const createUserSchema = z.object({
  name: z.string().min(1, "Name is required"),
  phone: z.string().min(1, "Phone is required"),
  countryId: z.number(),
});

// Schema for address - simplified for form
const addressSchema = z.object({
  apartment: z.string().nullable().optional(),
  block: z.string().nullable().optional(),
  streetName: z.string().min(1, "Street name is required"),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  lat: z.string(),
  long: z.string(),
});

type CreateUserForm = z.infer<typeof createUserSchema>;
type AddressForm = z.infer<typeof addressSchema>;

export default function CreateOrderPage() {
  // User state
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isCreateUserOpen, setIsCreateUserOpen] = useState(false);

  // Address state
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);
  const [isAddressDialogOpen, setIsAddressDialogOpen] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<{
    lat: number;
    lng: number;
  } | null>(null);

  // Warehouse state
  const [selectedWarehouse, setSelectedWarehouse] = useState<Warehouse | null>(
    null
  );

  // Cart state
  const [cart, setCart] = useState<any>(null);
  const [couponCode, setCouponCode] = useState("");
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [selectedOptions, setSelectedOptions] = useState<{
    [attributeId: number]: number;
  }>({});
  const [productQuantity, setProductQuantity] = useState(1);
  const [isProductDialogOpen, setIsProductDialogOpen] = useState(false);
  const [productDetails, setProductDetails] = useState<any>(null);

  // Forms
  const userForm = useForm<CreateUserForm>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      name: "",
      phone: "",
      countryId: 1,
    },
  });

  const addressForm = useForm<AddressForm>({
    resolver: zodResolver(addressSchema),
    defaultValues: {
      apartment: "",
      block: "",
      streetName: "",
      city: "",
      state: "",
      lat: "0",
      long: "0",
    },
  });

  // Internal state for hidden fields
  const [_zipCode, setZipCode] = useState("00000");
  const [_countryId] = useState(1);

  // Function to generate a random password
  const generateRandomPassword = () => {
    const length = 12;
    const charset =
      "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let password = "";
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  };

  // Function to create a new user
  const onCreateUser = async (data: CreateUserForm) => {
    try {
      const response = await apiService.post("auth/register", {
        ...data,
        password: generateRandomPassword(),
      });

      const user = response.data as User;
      setSelectedUser(user);
      setIsCreateUserOpen(false);
      toast.success("User created successfully");
      userForm.reset();
    } catch (error) {
      toast.error("Failed to create user", {
        description:
          error instanceof Error ? error.message : "Unknown error occurred",
      });
    }
  };

  // Function to fetch users for ItemPicker
  const fetchUsers = async (
    page: number,
    pageSize: number,
    searchQuery: string
  ): Promise<PaginatedResponse<User>> => {
    const response = await apiService.get("admin/users", {
      page,
      pageSize,
      search: searchQuery,
    });
    return response.data;
  };

  // Function to fetch warehouses for ItemPicker
  const fetchWarehouses = async (
    page: number,
    pageSize: number,
    searchQuery: string
  ): Promise<PaginatedResponse<Warehouse>> => {
    const response = await apiService.get("admin/warehouses", {
      page,
      pageSize,
      search: searchQuery,
    });
    return response.data;
  };

  // Function to fetch products for selection
  const fetchProducts = async (
    page: number,
    pageSize: number,
    searchQuery: string
  ): Promise<PaginatedResponse<Product>> => {
    if (!selectedWarehouse || !selectedAddress) {
      return { data: [], total: 0, page, pageSize, totalPages: 0 };
    }

    const response = await apiService.get("products", {
      page,
      pageSize,
      search: searchQuery,
      lat: selectedAddress.lat,
      long: selectedAddress.long,
      warehouseType: selectedWarehouse.type,
      overrideWarehouseId: selectedWarehouse.id,
    });
    return response.data;
  };

  // Function to fetch cart
  const fetchCart = useCallback(async () => {
    if (!selectedUser || !selectedAddress || !selectedWarehouse) return;

    try {
      const response = await apiService.get("cart", {
        lat: parseFloat(selectedAddress.lat),
        long: parseFloat(selectedAddress.long),
        warehouseType: selectedWarehouse.type,
        couponCode: couponCode ?? undefined,
        overrideUserId: selectedUser.id,
        overrideWarehouseId: selectedWarehouse.id,
      });
      setCart(response.data);
    } catch (error) {
      console.error("Failed to fetch cart:", error);
    }
  }, [selectedUser, selectedAddress, selectedWarehouse, couponCode]);

  // Fetch cart when dependencies change
  useEffect(() => {
    fetchCart();
  }, [fetchCart]);

  // Function to add product to cart
  const addToCart = async (
    productId: number,
    quantity: number,
    variationId?: number
  ) => {
    if (!selectedUser || !selectedAddress || !selectedWarehouse) {
      toast.error("Please select user, address, and warehouse first");
      return;
    }

    try {
      const params = new URLSearchParams({
        lat: selectedAddress.lat,
        long: selectedAddress.long,
        warehouseType: selectedWarehouse.type,
        overrideUserId: selectedUser.id.toString(),
        overrideWarehouseId: selectedWarehouse.id.toString(),
      });

      await apiService.post(`cart?${params.toString()}`, {
        productId,
        quantity,
        variationId,
      });

      await fetchCart(); // Refresh cart
      toast.success("Product added to cart");
    } catch (error) {
      toast.error("Failed to add product to cart", {
        description:
          error instanceof Error ? error.message : "Unknown error occurred",
      });
    }
  };

  // Function to remove item from cart
  const removeFromCart = async (cartItemId: number) => {
    if (!selectedAddress || !selectedWarehouse) return;

    try {
      const params = new URLSearchParams({
        lat: selectedAddress.lat,
        long: selectedAddress.long,
        warehouseType: selectedWarehouse.type,
        overrideWarehouseId: selectedWarehouse.id.toString(),
      });

      await apiService.delete(`cart/${cartItemId}?${params.toString()}`);

      await fetchCart(); // Refresh cart
      toast.success("Item removed from cart");
    } catch (error) {
      toast.error("Failed to remove item from cart");
    }
  };

  // Function to apply coupon
  const applyCoupon = async () => {
    await fetchCart(); // This will apply the coupon
  };

  // Function to fetch product details with selected options
  const fetchProductDetails = async (
    productId: number,
    optionIds: number[] = []
  ) => {
    if (!selectedAddress || !selectedWarehouse) return;

    try {
      const params: any = {
        lat: parseFloat(selectedAddress.lat),
        long: parseFloat(selectedAddress.long),
        warehouseType: selectedWarehouse.type,
        overrideWarehouseId: selectedWarehouse.id,
      };

      if (optionIds.length > 0) {
        params.optionIds = optionIds.join(",");
      }

      const response = await apiService.get(`products/${productId}`, params);
      setProductDetails(response.data);
      return response.data;
    } catch (error) {
      console.error("Failed to fetch product details:", error);
      return null;
    }
  };

  // Function to place order
  const placeOrder = async () => {
    if (
      !selectedUser ||
      !selectedAddress ||
      !selectedWarehouse ||
      !cart ||
      cart.items.length === 0
    ) {
      toast.error("Please fill in all required fields and add items to cart");
      return;
    }

    try {
      const orderData = {
        shippingAddressId: selectedAddress.id,
        deliveryDate: new Date().toISOString().split("T")[0], // Today's date
        deliveryStartTime: "09:00",
        deliveryEndTime: "18:00",
        note: "",
        lat: parseFloat(selectedAddress.lat),
        long: parseFloat(selectedAddress.long),
        warehouseType: "GENERAL",
        overrideWarehouseId: selectedWarehouse.id,
        couponCode: couponCode || undefined,
        useRewardPoints: false,
        cashToPay: 0,
      };

      const params = new URLSearchParams({
        overrideUserId: selectedUser.id.toString(),
      });

      await apiService.post(`orders?${params.toString()}`, orderData);
      toast.success("Order placed successfully");

      // Clear cart and reset form
      await apiService.delete(`cart/clear?overrideUserId=${selectedUser.id}`);
      setSelectedUser(null);
      setSelectedAddress(null);
      setSelectedWarehouse(null);
      setCart(null);
      setCouponCode("");
    } catch (error) {
      toast.error("Failed to place order", {
        description:
          error instanceof Error ? error.message : "Unknown error occurred",
      });
    }
  };

  // Update address form with location data
  const handleLocationSelect = async (lat: string, lng: string) => {
    // Update selected location
    setSelectedLocation({
      lat: parseFloat(lat),
      lng: parseFloat(lng),
    });

    // Update form values
    addressForm.setValue("lat", lat);
    addressForm.setValue("long", lng);

    // Use Google Places API to get address details
    try {
      if (typeof window !== "undefined" && window.google) {
        const geocoder = new window.google.maps.Geocoder();
        const response = await geocoder.geocode({
          location: { lat: parseFloat(lat), lng: parseFloat(lng) },
        });

        if (response.results && response.results.length > 0) {
          const addressComponents = response.results[0].address_components;

          // Extract address components
          const getComponent = (type: string, shortName = false) => {
            const component = addressComponents.find((c) =>
              c.types.includes(type)
            );
            return component
              ? shortName
                ? component.short_name
                : component.long_name
              : "";
          };

          // Set address fields from Google Places API
          const streetNumber = getComponent("street_number");
          const route = getComponent("route");
          const streetName = `${streetNumber} ${route}`.trim();

          if (streetName) {
            addressForm.setValue("streetName", streetName);
          }

          const city =
            getComponent("locality") ||
            getComponent("administrative_area_level_2");
          if (city) {
            addressForm.setValue("city", city);
          }

          const state = getComponent("administrative_area_level_1");
          if (state) {
            addressForm.setValue("state", state);
          }

          const zipCode = getComponent("postal_code");
          if (zipCode) {
            setZipCode(zipCode);
          }
        }
      }
    } catch (error) {
      console.error("Error getting address details:", error);
      // If geocoding fails, just continue with the coordinates
    }
  };

  // Reset address form and location when dialog opens
  const handleAddressDialogOpen = () => {
    setSelectedLocation(null);
    addressForm.reset({
      apartment: "",
      block: "",
      streetName: "",
      city: "",
      state: "",
      lat: "0",
      long: "0",
    });
    setIsAddressDialogOpen(true);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Create Manual Order</h1>
      </div>

      <Separator />

      <div className="grid grid-cols-3 gap-6">
        {/* Left Column - Main Form */}
        <div className="col-span-2 space-y-6">
          {/* User Selection Section */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <ItemPicker<User>
                    title="Select Customer"
                    selectionMode="single"
                    columns={[
                      {
                        header: "Name",
                        accessorKey: "name",
                      },
                      {
                        header: "Phone",
                        accessorKey: "phone",
                      },
                      {
                        header: "Email",
                        accessorKey: "email",
                      },
                    ]}
                    fetchItems={fetchUsers}
                    onConfirmSelection={(selected) => {
                      setSelectedUser(selected as User);
                    }}
                    initialSelectedItems={selectedUser ? [selectedUser] : []}
                    keyField="id"
                    searchPlaceholder="Search by name or phone..."
                    renderTrigger={() => (
                      <Button
                        variant="outline"
                        className="w-full justify-between"
                      >
                        {selectedUser ? selectedUser.name : "Select Customer"}
                      </Button>
                    )}
                  />
                </div>
                <Button
                  variant="outline"
                  className="ml-4"
                  onClick={() => setIsCreateUserOpen(true)}
                >
                  Create New
                </Button>
              </div>

              {selectedUser && (
                <div className="mt-4">
                  <div className="text-sm text-muted-foreground">
                    Selected Customer:
                  </div>
                  <div className="mt-1">
                    <Badge variant="secondary">{selectedUser.name}</Badge>
                    <Badge variant="secondary" className="ml-2">
                      {selectedUser.phone}
                    </Badge>
                    {selectedUser.email && (
                      <Badge variant="secondary" className="ml-2">
                        {selectedUser.email}
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Address Selection Section */}
          {selectedUser && (
            <Card>
              <CardHeader>
                <CardTitle>Delivery Address</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <ItemPicker<Address>
                      title="Select Address"
                      selectionMode="single"
                      columns={[
                        {
                          header: "Address",
                          render: (address) => (
                            <div>
                              {address.streetName}, {address.city},{" "}
                              {address.state}
                            </div>
                          ),
                        },
                      ]}
                      fetchItems={async (page, pageSize, search) => {
                        const response = await apiService.get(`addresses`, {
                          overrideUserId: selectedUser.id,
                          page,
                          pageSize,
                          search,
                        });
                        return {
                          data: response.data,
                          total: response.data.length,
                          page,
                          pageSize,
                          totalPages: 1,
                        } satisfies PaginatedResponse<Address>;
                      }}
                      onConfirmSelection={(selected) => {
                        setSelectedAddress(selected as Address);
                      }}
                      initialSelectedItems={
                        selectedAddress ? [selectedAddress] : []
                      }
                      keyField="id"
                      searchPlaceholder="Search address..."
                      renderTrigger={() => (
                        <Button
                          variant="outline"
                          className="w-full justify-between"
                        >
                          {selectedAddress
                            ? `${selectedAddress.streetName}, ${selectedAddress.city}`
                            : "Select Address"}
                        </Button>
                      )}
                    />
                  </div>
                  <Button
                    variant="outline"
                    className="ml-4"
                    onClick={handleAddressDialogOpen}
                  >
                    Add New
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Warehouse Selection */}
          {selectedAddress && (
            <Card>
              <CardHeader>
                <CardTitle>Select Warehouse</CardTitle>
              </CardHeader>
              <CardContent>
                <ItemPicker<Warehouse>
                  title="Select Warehouse"
                  selectionMode="single"
                  columns={[
                    {
                      header: "Name",
                      accessorKey: "name",
                    },
                    {
                      header: "Location",
                      render: (warehouse) => <div>{warehouse.address}</div>,
                    },
                  ]}
                  fetchItems={fetchWarehouses}
                  onConfirmSelection={(selected) => {
                    setSelectedWarehouse(selected as Warehouse);
                  }}
                  initialSelectedItems={
                    selectedWarehouse ? [selectedWarehouse] : []
                  }
                  keyField="id"
                  searchPlaceholder="Search warehouse..."
                  renderTrigger={() => (
                    <Button
                      variant="outline"
                      className="w-full justify-between"
                    >
                      {selectedWarehouse
                        ? selectedWarehouse.name
                        : "Select Warehouse"}
                    </Button>
                  )}
                />
              </CardContent>
            </Card>
          )}

          {/* Product Selection */}
          {selectedWarehouse && (
            <Card>
              <CardHeader>
                <CardTitle>Add Products</CardTitle>
              </CardHeader>
              <CardContent>
                <Button
                  onClick={() => setIsProductDialogOpen(true)}
                  className="w-full"
                >
                  Add Product to Cart
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Column - Cart */}
        <div className="col-span-1">
          <Card className="sticky top-6">
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {!cart || cart.items.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  No items in cart
                </div>
              ) : (
                <>
                  {/* Cart Items */}
                  <div className="space-y-3">
                    {cart.items.map((item: any) => (
                      <div
                        key={`${item.id}`}
                        className="border rounded-lg overflow-hidden bg-white shadow-sm"
                      >
                        {/* Item Details Section */}
                        <div className="p-4 bg-gray-50">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h4 className="font-medium text-gray-900 leading-tight">
                                {item.product.name}
                              </h4>
                              {item.variation && item.variation.options && (
                                <div className="flex flex-wrap gap-2 mt-2">
                                  {item.variation.options.map((option: any) => (
                                    <div
                                      key={option.option.id}
                                      className="flex items-center gap-1.5 bg-white border border-gray-200 rounded-md px-2 py-1"
                                    >
                                      {option.option.colorCode && (
                                        <div
                                          className="w-3 h-3 rounded-full border border-gray-300"
                                          style={{
                                            backgroundColor:
                                              option.option.colorCode,
                                          }}
                                        />
                                      )}
                                      <span className="text-xs font-medium text-gray-600">
                                        {option.option.attribute.name}:
                                      </span>
                                      <span className="text-xs text-gray-800">
                                        {option.option.name}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFromCart(item.id)}
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        {/* Price and Quantity Section */}
                        <div className="p-4 bg-white border-t border-gray-100">
                          <div className="flex items-center justify-between">
                            <div className="flex flex-col">
                              <span className="text-sm font-semibold text-gray-900">
                                Rs. {item.itemTotal}
                              </span>
                            </div>
                            <div className="flex items-center gap-3">
                              <div className="flex items-center border border-gray-200 rounded-md">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    addToCart(
                                      item.productId,
                                      -1,
                                      item.variationId
                                    )
                                  }
                                  disabled={item.quantity <= 1}
                                  className="h-8 w-8 p-0 hover:bg-gray-100"
                                >
                                  -
                                </Button>
                                <span className="w-12 text-center text-sm font-medium">
                                  {item.quantity}
                                </span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    addToCart(
                                      item.productId,
                                      1,
                                      item.variationId
                                    )
                                  }
                                  className="h-8 w-8 p-0 hover:bg-gray-100"
                                >
                                  +
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Coupon Input */}
                  <div className="flex gap-2">
                    <Input
                      placeholder="Enter coupon code"
                      value={couponCode}
                      onChange={(e) => setCouponCode(e.target.value)}
                    />
                    <Button variant="outline" onClick={applyCoupon}>
                      Apply
                    </Button>
                  </div>

                  {/* Order Summary */}
                  <div className="space-y-2 pt-4 border-t">
                    <div className="flex justify-between">
                      <span>Subtotal</span>
                      <span>Rs. {cart.subtotal}</span>
                    </div>
                    {cart.handlingCharge &&
                      parseFloat(cart.handlingCharge) > 0 && (
                        <div className="flex justify-between">
                          <span>Handling Charge</span>
                          <span>Rs. {cart.handlingCharge}</span>
                        </div>
                      )}
                    {cart.deliveryFee && parseFloat(cart.deliveryFee) > 0 && (
                      <div className="flex justify-between">
                        <span>Delivery Fee</span>
                        <span>Rs. {cart.deliveryFee}</span>
                      </div>
                    )}
                    {cart.couponDiscount &&
                      parseFloat(cart.couponDiscount) > 0 && (
                        <div className="flex justify-between text-green-600">
                          <span>Coupon Discount</span>
                          <span>-Rs. {cart.couponDiscount}</span>
                        </div>
                      )}
                    {cart.rewardDiscount &&
                      parseFloat(cart.rewardDiscount) > 0 && (
                        <div className="flex justify-between text-green-600">
                          <span>Reward Discount</span>
                          <span>-Rs. {cart.rewardDiscount}</span>
                        </div>
                      )}
                    <div className="flex justify-between font-medium">
                      <span>Total</span>
                      <span>Rs. {cart.total}</span>
                    </div>
                  </div>

                  {/* Place Order Button */}
                  <Button
                    className="w-full"
                    size="lg"
                    onClick={placeOrder}
                    disabled={
                      !selectedUser || !selectedAddress || !selectedWarehouse
                    }
                  >
                    Place Order
                  </Button>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Create User Dialog */}
      <Dialog open={isCreateUserOpen} onOpenChange={setIsCreateUserOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Customer</DialogTitle>
          </DialogHeader>
          <form
            onSubmit={userForm.handleSubmit(onCreateUser)}
            className="space-y-4"
          >
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                {...userForm.register("name")}
                placeholder="Enter customer name"
              />
              {userForm.formState.errors.name && (
                <p className="text-sm text-red-500">
                  {userForm.formState.errors.name.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                {...userForm.register("phone")}
                placeholder="Enter phone number"
              />
              {userForm.formState.errors.phone && (
                <p className="text-sm text-red-500">
                  {userForm.formState.errors.phone.message}
                </p>
              )}
            </div>

            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsCreateUserOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit">Create Customer</Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Create Address Dialog */}
      <Dialog open={isAddressDialogOpen} onOpenChange={setIsAddressDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Address</DialogTitle>
          </DialogHeader>
          <form
            onSubmit={addressForm.handleSubmit(async (data) => {
              try {
                const response = await apiService.post(
                  `addresses?overrideUserId=${selectedUser?.id}`,
                  {
                    ...data,
                    type: "SHIPPING", // Always use SHIPPING type
                    countryId: _countryId, // Use internal state
                    zipCode: _zipCode || "00000", // Use internal state or default
                  }
                );
                setSelectedAddress(response.data);
                setIsAddressDialogOpen(false);
                setSelectedLocation(null);
                toast.success("Address added successfully");
                addressForm.reset();
              } catch (error) {
                toast.error("Failed to add address", {
                  description:
                    error instanceof Error
                      ? error.message
                      : "Unknown error occurred",
                });
              }
            })}
            className="space-y-4"
          >
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="block">Block</Label>
                <Input
                  id="block"
                  {...addressForm.register("block")}
                  placeholder="Enter block (optional)"
                />
                {addressForm.formState.errors.block && (
                  <p className="text-sm text-red-500">
                    {addressForm.formState.errors.block.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="apartment">Apartment</Label>
                <Input
                  id="apartment"
                  {...addressForm.register("apartment")}
                  placeholder="Enter apartment (optional)"
                />
                {addressForm.formState.errors.apartment && (
                  <p className="text-sm text-red-500">
                    {addressForm.formState.errors.apartment.message}
                  </p>
                )}
              </div>

              <div className="space-y-2 col-span-2">
                <Label htmlFor="streetName">Street Name *</Label>
                <Input
                  id="streetName"
                  {...addressForm.register("streetName")}
                  placeholder="Enter street name"
                />
                {addressForm.formState.errors.streetName && (
                  <p className="text-sm text-red-500">
                    {addressForm.formState.errors.streetName.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="city">City *</Label>
                <Input
                  id="city"
                  {...addressForm.register("city")}
                  placeholder="Enter city"
                />
                {addressForm.formState.errors.city && (
                  <p className="text-sm text-red-500">
                    {addressForm.formState.errors.city.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="state">State *</Label>
                <Input
                  id="state"
                  {...addressForm.register("state")}
                  placeholder="Enter state"
                />
                {addressForm.formState.errors.state && (
                  <p className="text-sm text-red-500">
                    {addressForm.formState.errors.state.message}
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <Label>Select Location</Label>
              <div className="h-[400px] rounded-lg border overflow-hidden">
                <MapPicker
                  initialLat={selectedLocation?.lat.toString()}
                  initialLng={selectedLocation?.lng.toString()}
                  onLocationSelect={handleLocationSelect}
                />
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsAddressDialogOpen(false);
                  setSelectedLocation(null);
                  addressForm.reset();
                }}
              >
                Cancel
              </Button>
              <Button type="submit">Create Address</Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Product Selection Dialog */}
      <Dialog open={isProductDialogOpen} onOpenChange={setIsProductDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add Product to Cart</DialogTitle>
          </DialogHeader>
          <div className="space-y-6">
            {/* Product Selection */}
            <div>
              <Label className="mb-2">Select Product</Label>
              <ItemPicker<Product>
                title="Select Product"
                selectionMode="single"
                columns={[
                  {
                    header: "Name",
                    accessorKey: "name",
                  },
                  {
                    header: "Price",
                    render: (product) => <div>Rs. {product.price}</div>,
                  },
                  {
                    header: "Stock",
                    render: (product) => <div>{product.quantity}</div>,
                  },
                ]}
                fetchItems={fetchProducts}
                onConfirmSelection={async (selected) => {
                  const product = selected as Product;
                  setSelectedProduct(product);
                  setSelectedOptions({});
                  setProductDetails(null);

                  // Fetch initial product details
                  await fetchProductDetails(product.id);
                }}
                initialSelectedItems={selectedProduct ? [selectedProduct] : []}
                keyField="id"
                searchPlaceholder="Search products..."
                renderTrigger={() => (
                  <Button
                    variant="outline"
                    className="w-full justify-between h-auto min-h-[40px] text-left"
                  >
                    <span className="truncate">
                      {selectedProduct
                        ? selectedProduct.name
                        : "Select Product"}
                    </span>
                  </Button>
                )}
              />
            </div>

            {/* Product Details */}
            {selectedProduct && (
              <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
                <div>
                  <h3 className="font-medium text-lg break-words">
                    {selectedProduct.name}
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Base Price: Rs.
                    {productDetails?.price || selectedProduct.price}
                  </p>
                </div>

                {/* Attribute Selection */}
                {productDetails?.attributes &&
                  productDetails.attributes.length > 0 && (
                    <div className="space-y-4">
                      {productDetails.attributes.map((attribute: any) => (
                        <div key={attribute.id}>
                          <Label>{attribute.name}</Label>
                          <div className="grid grid-cols-3 gap-2 mt-2">
                            {attribute.options.map((option: any) => (
                              <div
                                key={option.id}
                                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                                  selectedOptions[attribute.id] === option.id
                                    ? "border-blue-500 bg-blue-50"
                                    : "border-gray-200 hover:border-gray-300"
                                }`}
                                onClick={async () => {
                                  const newOptions = {
                                    ...selectedOptions,
                                    [attribute.id]: option.id,
                                  };
                                  setSelectedOptions(newOptions);

                                  // Fetch updated product details with selected options
                                  const optionIds = Object.values(newOptions);
                                  await fetchProductDetails(
                                    selectedProduct.id,
                                    optionIds
                                  );
                                }}
                              >
                                <div className="flex items-center gap-2">
                                  {option.colorCode && (
                                    <div
                                      className="w-4 h-4 rounded border"
                                      style={{
                                        backgroundColor: option.colorCode,
                                      }}
                                    />
                                  )}
                                  {option.imageUrl && (
                                    <img
                                      src={option.imageUrl}
                                      alt={option.name}
                                      className="w-6 h-6 object-cover rounded"
                                    />
                                  )}
                                  <span className="text-sm font-medium">
                                    {option.name}
                                  </span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                {/* Stock and Variation Info */}
                {productDetails?.selectedVariation && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="font-medium">Selected Variation</div>
                        <div className="text-sm text-muted-foreground">
                          {Object.values(selectedOptions).length > 0
                            ? productDetails.attributes
                                ?.map(
                                  (attr: any) =>
                                    attr.options.find(
                                      (opt: any) =>
                                        opt.id === selectedOptions[attr.id]
                                    )?.name
                                )
                                .filter(Boolean)
                                .join(" + ")
                            : "Base Product"}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">
                          Rs. {productDetails.price}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Stock: {productDetails.quantity}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Quantity Selection */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Quantity</Label>
                    <Input
                      type="number"
                      min="1"
                      max={
                        productDetails?.quantity ??
                        selectedProduct.quantity ??
                        0
                      }
                      value={productQuantity}
                      onChange={(e) =>
                        setProductQuantity(parseInt(e.target.value) || 1)
                      }
                    />
                  </div>
                  <div>
                    <Label>Total Price</Label>
                    <div className="h-10 px-3 py-2 border rounded-md bg-gray-50 flex items-center">
                      Rs.
                      {(
                        (productDetails?.price || selectedProduct.price) *
                        productQuantity
                      ).toFixed(2)}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            {selectedProduct && (
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsProductDialogOpen(false);
                    setSelectedProduct(null);
                    setSelectedOptions({});
                    setProductDetails(null);
                    setProductQuantity(1);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={async () => {
                    await addToCart(
                      selectedProduct.id,
                      productQuantity,
                      productDetails?.selectedVariation?.id
                    );
                    setIsProductDialogOpen(false);
                    setSelectedProduct(null);
                    setSelectedOptions({});
                    setProductDetails(null);
                    setProductQuantity(1);
                  }}
                  disabled={
                    (productDetails?.attributes &&
                      productDetails.attributes.length > 0 &&
                      Object.keys(selectedOptions).length <
                        productDetails.attributes.length) ||
                    productDetails?.quantity === 0
                  }
                >
                  Add to Cart
                </Button>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
