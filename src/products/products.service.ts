import { Injectable, NotFoundException } from '@nestjs/common';
import { Prisma, WarehouseType } from '@prisma/client';
import { InventoryService } from 'src/admin/inventories/inventories.service';
import { WarehouseService } from 'src/admin/warehouse/warehouse.service';
import { Context } from 'src/context';
import { Exception, NoWarehouseException } from 'src/exceptions';
import { PrismaService } from 'src/prisma/prisma.service';
import { ProductTransformer } from 'src/transformers/product.transformer';
import { ProductQueryResult, ProductWithStockResponse } from 'src/types';

@Injectable()
export class ProductsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly inventoryService: InventoryService,
    private readonly warehouseService: WarehouseService,
    private readonly productTransformer: ProductTransformer,
    private readonly context: Context,
  ) {}

  async getProducts(params: {
    page: number;
    pageSize: number;
    categoryId?: number;
    userLocation: { lat: number; long: number };
    warehouseType: WarehouseType;
    lang: string;
    search?: string;
  }) {
    const {
      page,
      pageSize,
      categoryId,
      userLocation,
      warehouseType,
      lang,
      search,
    } = params;

    const warehouse = await this.warehouseService.getNearestWarehouse(
      {
        lat: userLocation.lat,
        long: userLocation.long,
      },
      warehouseType,
    );

    if (!warehouse) {
      throw new NoWarehouseException();
    }

    const language = await this.prisma.language.findUniqueOrThrow({
      where: { code: lang },
    });

    // Prepare the where clause for product filtering
    const whereClause: Prisma.ProductWhereInput = {
      // Only show active products
      active: true,
    };

    // If categoryId is provided, check if the category has children
    if (categoryId) {
      // Get the category and its children
      const category = await this.prisma.category.findUnique({
        where: { id: categoryId },
        include: {
          children: true,
        },
      });

      if (category) {
        // If the category has children, include products from all of them
        if (category.children && category.children.length > 0) {
          const childCategoryIds = category.children.map((child) => child.id);
          whereClause.OR = [
            { categoryId: categoryId },
            { categoryId: { in: childCategoryIds } },
          ];
        } else {
          // If no children, just use the original category
          whereClause.categoryId = categoryId;
        }
      } else {
        // If category not found, default to the original behavior
        whereClause.categoryId = categoryId;
      }
    }

    // Handle search functionality
    if (search && search.trim() !== '') {
      const searchConditions: Prisma.ProductWhereInput[] = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { barcode: { contains: search, mode: 'insensitive' } },
      ];

      // If we already have OR conditions from category filtering, we need to combine them with search
      if (whereClause.OR) {
        // Store the existing category conditions
        const categoryConditions = whereClause.OR;
        whereClause.OR = undefined;
        whereClause.AND = [
          // Products must match the category conditions
          { OR: categoryConditions },
          // AND must match at least one search condition
          { OR: searchConditions },
        ];
      } else {
        // If no existing OR conditions, just add the search conditions
        whereClause.OR = searchConditions;
      }
    }

    // eslint-disable-next-line prefer-const
    let [products, total] = await this.prisma.$transaction([
      this.prisma.product.findMany({
        where: whereClause,
        include: {
          media: true,
          thumbnail: true,
          category: {
            include: {
              categoryTranslation: {
                where: { languageId: language.id },
                take: 1,
              },
            },
          },
          productTranslation: {
            where: { languageId: language.id },
            take: 1,
          },
          productPolicies: {
            include: {
              productPolicyType: true,
            },
          },
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.product.count({ where: whereClause }),
    ]);

    const productsWithQuantityAndPrice: ProductWithStockResponse[] =
      await Promise.all(
        products.map(async (product) => {
          return this.productTransformer.getProductWithStockResponse(
            product,
            warehouse.id,
          );
        }),
      );

    return {
      data: productsWithQuantityAndPrice,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async getProduct(params: {
    id: number;
    userLocation: { lat: number; long: number };
    warehouseType: WarehouseType;
    lang: string;
    variationId?: number;
    optionIds?: number[];
  }) {
    const { id, userLocation, warehouseType, lang, variationId, optionIds } =
      params;

    const warehouse = await this.warehouseService.getNearestWarehouse(
      {
        lat: userLocation.lat,
        long: userLocation.long,
      },
      warehouseType,
    );

    if (!warehouse) {
      throw new Exception('Warehouse not found');
    }

    const language = await this.prisma.language.findUniqueOrThrow({
      where: { code: lang },
    });

    // First get the basic product information
    const productData = await this.prisma.product.findUniqueOrThrow({
      where: {
        id: id,
      },
      include: {
        media: true,
        thumbnail: true,
        category: {
          include: {
            categoryTranslation: {
              where: { languageId: language.id },
              take: 1,
            },
          },
        },
        productTranslation: {
          where: { languageId: language.id },
          take: 1,
        },
        productPolicies: {
          include: {
            productPolicyType: true,
          },
        },
      },
    });

    if (!productData.active) {
      if (this.context.user?.type !== 'ADMIN') {
        throw new NotFoundException('Product not found');
      }
    }

    // Then get the product attributes separately
    const productAttributes = await this.prisma.productAttribute.findMany({
      where: {
        productId: id,
        deletedAt: null,
      },
      include: {
        options: {
          where: {
            deletedAt: null,
          },
        },
      },
    });

    // Combine them into the ProductQueryResult
    const product: ProductQueryResult = {
      ...productData,
      attributes: productAttributes.map((attr) => ({
        id: attr.id,
        name: attr.name,
        options: attr.options.map((opt) => ({
          id: opt.id,
          name: opt.name,
          colorCode: opt.colorCode,
          imageUrl: opt.imageUrl,
        })),
      })),
    };

    // If optionIds are provided, find the matching variation
    let selectedVariation:
      | Prisma.ProductVariationGetPayload<{
          include: {
            options: {
              include: {
                option: {
                  include: {
                    attribute: true;
                  };
                };
              };
            };
            media: true;
          };
        }>
      | undefined = undefined;
    if (optionIds && optionIds.length > 0 && product.hasVariations) {
      try {
        // Find all variations for this product
        const variations = await this.prisma.productVariation.findMany({
          where: {
            productId: id,
            deletedAt: null, // Only consider non-deleted variations
          },
          include: {
            options: {
              include: {
                option: {
                  include: {
                    attribute: true,
                  },
                },
              },
            },
            media: true,
          },
        });

        // Find the variation that has exactly the selected options
        selectedVariation = variations.find((variation) => {
          // Get all option IDs for this variation
          const variationOptionIds = variation.options.map(
            (mapping) => mapping.optionId,
          );

          // Check if the variation has exactly the same options as selected
          return (
            // All selected options must be in the variation
            optionIds.every((optId) => variationOptionIds.includes(optId)) &&
            // The variation must have the same number of options as selected
            variationOptionIds.length === optionIds.length
          );
        });
      } catch (error) {
        // If there's an error finding the variation, just continue without it
        console.error('Error finding variation by option IDs:', error);
      }
    }

    // If we found a matching variation, use its ID for the transformer
    // Otherwise, use the variationId parameter if provided
    const effectiveVariationId = selectedVariation
      ? selectedVariation.id
      : variationId;

    return this.productTransformer.getProductWithStockResponse(
      product,
      warehouse.id,
      effectiveVariationId,
      selectedVariation,
    );
  }

  async getRelatedProducts(params: {
    id: number;
    userLocation: { lat: number; long: number };
    warehouseType: WarehouseType;
    lang: string;
  }) {
    const { id, userLocation, warehouseType, lang } = params;

    const warehouse = await this.warehouseService.getNearestWarehouse(
      {
        lat: userLocation.lat,
        long: userLocation.long,
      },
      warehouseType,
    );

    if (!warehouse) {
      throw new NoWarehouseException();
    }

    const language = await this.prisma.language.findUniqueOrThrow({
      where: { code: lang },
    });

    // Get related products for the given product ID
    const relatedProductsData = await this.prisma.relatedProduct.findMany({
      where: {
        productId: id,
        relatedProduct: {
          active: true,
        },
      },
      include: {
        relatedProduct: {
          include: {
            media: true,
            thumbnail: true,
            category: {
              include: {
                categoryTranslation: {
                  where: { languageId: language.id },
                  take: 1,
                },
              },
            },
            productTranslation: {
              where: { languageId: language.id },
              take: 1,
            },
            productPolicies: {
              include: {
                productPolicyType: true,
              },
            },
          },
        },
      },
    });

    // Extract the related products and filter only active ones
    const relatedProducts = relatedProductsData.map(
      (relation) => relation.relatedProduct,
    );

    // Transform products to include stock information
    const productsWithStock: ProductWithStockResponse[] = await Promise.all(
      relatedProducts.map(async (product) => {
        return this.productTransformer.getProductWithStockResponse(
          product,
          warehouse.id,
        );
      }),
    );

    return productsWithStock;
  }

  async subscribeToProductAvailability(productId: number) {
    return await this.prisma.productAvailabilitySubscription.upsert({
      where: {
        userId_productId: { userId: this.context.user!.id, productId },
      },
      update: {},
      create: {
        userId: this.context.user!.id,
        productId,
      },
    });
  }
}
